<?php 
  session_start();
  include_once "php/config.php";
  if(!isset($_SESSION['unique_id'])){
    header("location: login.php");
  }
?>
<?php include_once "header.php"; ?>
<body>
  <div class="wrapper">
    <section class="chat-area">
      <header>
        <?php
          $user_id = mysqli_real_escape_string($conn, $_GET['user_id']);
          $sql = mysqli_query($conn, "SELECT * FROM users WHERE unique_id = {$user_id}");
          if(mysqli_num_rows($sql) > 0){
            $row = mysqli_fetch_assoc($sql);
          }else{
            header("location: users.php");
          }
        ?>
        <a href="users.php" class="back-icon"><i class="fas fa-arrow-left"></i></a>
        <img src="php/images/<?php echo $row['img']; ?>" alt="">
        <div class="details">
          <span><?php echo $row['fname']. " " . $row['lname'] ?></span>
          <p><?php echo $row['status']; ?></p>
        </div>
        <?php if($_SESSION['type'] == 0 && $row['type'] == 1): ?>
        <button class="details-btn" onclick="openTourGuideDetails()">
          <i class="fas fa-info-circle"></i>
        </button>
        <?php endif; ?>
      </header>
      <div class="chat-box">

      </div>
      <form action="#" class="typing-area">
        <input type="text" class="incoming_id" name="incoming_id" value="<?php echo $user_id; ?>" hidden>
        <input type="text" name="message" class="input-field" placeholder="Type a message here..." autocomplete="off">
        <button><i class="fab fa-telegram-plane"></i></button>
      </form>
    </section>
  </div>

  <!-- Tour Guide Details Modal -->
  <?php if($_SESSION['type'] == 0 && $row['type'] == 1): ?>
  <div id="tourGuideModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Tour Guide Details</h2>
        <span class="close" onclick="closeTourGuideDetails()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="guide-profile">
          <div class="guide-image">
            <img src="php/images/<?php echo $row['img']; ?>" alt="Tour Guide">
          </div>
          <div class="guide-info">
            <h3><?php echo $row['fname']. " " . $row['lname'] ?></h3>
            <p class="guide-title">Professional Tour Guide</p>
            <div class="guide-rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <span>4.9 (127 reviews)</span>
            </div>
          </div>
        </div>

        <div class="guide-details">
          <div class="detail-section">
            <h4><i class="fas fa-user"></i> About</h4>
            <p>Experienced tour guide with over 5 years of expertise in showing visitors the best of our beautiful city. Passionate about history, culture, and creating memorable experiences for travelers.</p>
          </div>

          <div class="detail-section">
            <h4><i class="fas fa-language"></i> Languages</h4>
            <div class="languages">
              <span class="language-tag">English</span>
              <span class="language-tag">Arabic</span>
              <span class="language-tag">French</span>
            </div>
          </div>

          <div class="detail-section">
            <h4><i class="fas fa-map-marker-alt"></i> Specialties</h4>
            <div class="specialties">
              <span class="specialty-tag">Historical Tours</span>
              <span class="specialty-tag">Cultural Sites</span>
              <span class="specialty-tag">Food Tours</span>
              <span class="specialty-tag">Adventure Tours</span>
            </div>
          </div>

          <div class="detail-section">
            <h4><i class="fas fa-clock"></i> Experience</h4>
            <p>5+ years of professional guiding experience</p>
          </div>

          <div class="detail-section">
            <h4><i class="fas fa-certificate"></i> Certifications</h4>
            <ul>
              <li>Licensed Tour Guide</li>
              <li>First Aid Certified</li>
              <li>Cultural Heritage Specialist</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <script src="javascript/chat.js"></script>

</body>
</html>
