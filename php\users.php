<?php
    session_start();
    include_once "config.php";
    $outgoing_id = $_SESSION['unique_id'];
    if($_SESSION['type'] == 0){
    $sql = "SELECT * FROM users WHERE NOT unique_id = {$outgoing_id} AND type = 1 ORDER BY user_id DESC";
    }else{
        $sql = "SELECT DISTINCT u.* FROM users u 
                INNER JOIN messages m ON (m.outgoing_msg_id = u.unique_id)
                WHERE m.incoming_msg_id = {$outgoing_id} 
                AND u.type = 0
                AND u.unique_id != {$outgoing_id} 
                ORDER BY u.user_id DESC";
    }
    $query = mysqli_query($conn, $sql);
    $output = "";
    if(mysqli_num_rows($query) == 0){
        $output .= "No users are available to chat";
    }elseif(mysqli_num_rows($query) > 0){
        include_once "data.php";
    }
    echo $output;
?>