<?php
    session_start();
    include_once "config.php";

    $outgoing_id = $_SESSION['unique_id'];
    $searchTerm = mysqli_real_escape_string($conn, $_POST['searchTerm']);
    if($_SESSION['type'] == 0){
        $sql = "SELECT * FROM users WHERE NOT unique_id = {$outgoing_id} AND (fname LIKE '%{$searchTerm}%' OR lname LIKE '%{$searchTerm}%') AND type = 1 ";
    }else{
        $sql = "SELECT DISTINCT u.* FROM users u 
                INNER JOIN messages m ON (m.outgoing_msg_id = u.unique_id)
                WHERE m.incoming_msg_id = {$outgoing_id} 
                AND u.type = 0
                AND u.unique_id != {$outgoing_id}
                AND (u.fname LIKE '%{$searchTerm}%' OR u.lname LIKE '%{$searchTerm}%') 
                ORDER BY u.user_id DESC";
    }
    $output = "";
    $query = mysqli_query($conn, $sql);
    if(mysqli_num_rows($query) > 0){
        include_once "data.php";
    }else{
        $output .= 'No user found related to your search term';
    }
    echo $output;
?>